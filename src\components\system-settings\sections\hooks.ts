import { useState, useCallback, useEffect } from "react";

export interface EmergencyContact {
  id: string;
  phoneNumber: string;
  type: string;
  contactType: "primary" | "backup";
}

export interface EmergencyContactFormData {
  phoneNumber: string;
  type: string;
}

export interface EmergencyContactFormErrors {
  phoneNumber: string;
  type: string;
}

export const useEmergencyContactForm = (
  onSubmit: (formData: EmergencyContactFormData) => Promise<void>,
  editingContact?: EmergencyContact | null
) => {
  const [formData, setFormData] = useState<EmergencyContactFormData>({
    phoneNumber: editingContact?.phoneNumber || "",
    type: editingContact?.type || "",
  });

  const [errors, setErrors] = useState<EmergencyContactFormErrors>({
    phoneNumber: "",
    type: "",
  });

  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  // Update form data when editing contact changes
  useEffect(() => {
    if (editingContact) {
      setFormData({
        phoneNumber: editingContact.phoneNumber,
        type: editingContact.type,
      });
      setErrors({
        phoneNumber: "",
        type: "",
      });
      setSubmitAttempted(false);
    }
  }, [editingContact]);

  // Validation functions
  const validatePhoneNumber = useCallback((phone: string): boolean => {
    if (!phone.trim()) return false;
    // Only allow numbers, spaces, hyphens, parentheses, and plus sign
    const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, "");
    // Must be all digits and at least 10 digits long
    const phoneRegex = /^\d{10,15}$/;
    return phoneRegex.test(cleanPhone);
  }, []);

  const validateType = useCallback((type: string): boolean => {
    return type.trim() !== "";
  }, []);

  const validateField = useCallback((field: keyof EmergencyContactFormData, value: string) => {
    switch (field) {
      case "phoneNumber":
        if (!value.trim()) return "Phone number is required";
        return validatePhoneNumber(value) ? "" : "Please enter a valid phone number";
      case "type":
        return validateType(value) ? "" : "Please select a contact type";
      default:
        return "";
    }
  }, [validatePhoneNumber, validateType]);

  const handleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
    const field = id as keyof EmergencyContactFormData;

    // For phone number, only allow numbers, spaces, hyphens, parentheses, and plus sign
    if (field === "phoneNumber") {
      const filteredValue = value.replace(/[^0-9\s\-\(\)\+]/g, "");
      setFormData(prev => ({ ...prev, [field]: filteredValue }));

      // Real-time validation
      const error = validateField(field, filteredValue);
      setErrors(prev => ({ ...prev, [field]: error }));
    } else {
      setFormData(prev => ({ ...prev, [field]: value }));

      // Real-time validation
      const error = validateField(field, value);
      setErrors(prev => ({ ...prev, [field]: error }));
    }
  }, [validateField]);

  const handleTypeChange = useCallback((type: string) => {
    setFormData(prev => ({ ...prev, type }));
    
    // Real-time validation for type
    const error = validateField("type", type);
    setErrors(prev => ({ ...prev, type: error }));
  }, [validateField]);

  const handleFocus = useCallback((field: string) => {
    setFocusedField(field);
  }, []);

  const handleBlur = useCallback(() => {
    setFocusedField(null);
  }, []);

  const isFormValid = useCallback(() => {
    const phoneValid = validatePhoneNumber(formData.phoneNumber);
    const typeValid = validateType(formData.type);
    return phoneValid && typeValid;
  }, [formData, validatePhoneNumber, validateType]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitAttempted(true);

    // Validate all fields
    const newErrors: EmergencyContactFormErrors = {
      phoneNumber: validateField("phoneNumber", formData.phoneNumber),
      type: validateField("type", formData.type),
    };

    setErrors(newErrors);

    // Check if form is valid
    if (Object.values(newErrors).every(error => error === "")) {
      await onSubmit(formData);
    }
  }, [formData, validateField, onSubmit]);

  const resetForm = useCallback(() => {
    setFormData({
      phoneNumber: "",
      type: "",
    });
    setErrors({
      phoneNumber: "",
      type: "",
    });
    setFocusedField(null);
    setSubmitAttempted(false);
  }, []);

  return {
    formData,
    errors,
    focusedField,
    submitAttempted,
    handleChange,
    handleTypeChange,
    handleFocus,
    handleBlur,
    handleSubmit,
    isFormValid: isFormValid(),
    resetForm,
  };
};
