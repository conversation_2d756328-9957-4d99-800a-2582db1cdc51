"use client";
import React from "react";
import { DetailedModal } from "@/components/Modal";
import { SubmitButton } from "@/components/button";
import { FaRegCircleCheck } from "react-icons/fa6";

interface EmergencyContactSuccessModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const EmergencyContactSuccessModal: React.FC<EmergencyContactSuccessModalProps> = ({
  isOpen,
  onClose,
}) => {
  return (
    <DetailedModal isOpen={isOpen} onClose={onClose} showCloseButton={false}>
      <div className="w-full text-center py-8">
        {/* Success Icon */}
        <div className="flex justify-center mb-6">
          <div className="flex items-center justify-center">
            <FaRegCircleCheck size={120} className="text-[#A3A3A3]" />
          </div>
        </div>

        {/* Success Message */}
        <div className="mb-8">
          <h2 className="text-lg lg:text-xl text-white font-medium mb-4">
            Emergency Number Added
          </h2>
        </div>

        {/* Action Button */}
        <div className="flex justify-center">
          <SubmitButton label="Done" onClick={onClose} />
        </div>
      </div>
    </DetailedModal>
  );
};

export default EmergencyContactSuccessModal;
