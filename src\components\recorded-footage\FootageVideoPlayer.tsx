"use client";
import React, { useRef, useState } from "react";
import { RecordedFootage, formatDuration, getRecordingTypeColor, getRecordingTypeLabel } from "@/lib/recordedFootage";
import { formatDateTime } from "@/utils/dateUtils";
import { FiX, FiDownload, FiMaximize2, FiMinimize2 } from "react-icons/fi";
import { BsPersonCheck } from "react-icons/bs";
import { HiVolumeUp } from "react-icons/hi";
import { MdMotionPhotosOn } from "react-icons/md";

interface FootageVideoPlayerProps {
  footage: RecordedFootage;
  isOpen: boolean;
  onClose: () => void;
}

const FootageVideoPlayer: React.FC<FootageVideoPlayerProps> = ({
  footage,
  isOpen,
  onClose,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const handleDownload = () => {
    // TODO: Implement download functionality
    console.log("Download footage:", footage.id);
  };

  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
      <div className="bg-[#1F1F1F] border border-[#2E2E2E] rounded-lg w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-[#2E2E2E]">
          <div className="flex items-center gap-3">
            <div className={`${getRecordingTypeColor(footage.recordingType)} text-white px-2 py-1 rounded text-xs font-medium`}>
              {getRecordingTypeLabel(footage.recordingType)}
            </div>
            <div className="text-white">
              <h2 className="text-lg font-medium">{footage.cameraName}</h2>
              <p className="text-sm text-[#8A8A8A]">{footage.location}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <button
              type="button"
              onClick={handleDownload}
              className="p-2 text-[#8A8A8A] hover:text-white transition-colors"
              title="Download footage"
            >
              <FiDownload size={18} />
            </button>
            <button
              type="button"
              onClick={toggleFullscreen}
              className="p-2 text-[#8A8A8A] hover:text-white transition-colors"
              title="Toggle fullscreen"
            >
              {isFullscreen ? <FiMinimize2 size={18} /> : <FiMaximize2 size={18} />}
            </button>
            <button
              type="button"
              onClick={onClose}
              className="p-2 text-[#8A8A8A] hover:text-white transition-colors"
              title="Close"
            >
              <FiX size={18} />
            </button>
          </div>
        </div>

        {/* Video Player */}
        <div className="relative bg-black">
          <video
            ref={videoRef}
            className="w-full h-[60vh] object-contain"
            controls
            autoPlay
            poster={footage.thumbnailUrl}
          >
            <source src={footage.videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          
          {/* Alert indicator overlay */}
          {footage.alertTriggered && (
            <div className="absolute top-4 right-4 bg-red-600 text-white px-3 py-1 rounded text-sm font-medium animate-pulse">
              ALERT
            </div>
          )}
        </div>

        {/* Footer with metadata */}
        <div className="p-4 bg-[#1A1A1A]">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
            {/* Time info */}
            <div>
              <h4 className="text-[#8A8A8A] text-xs uppercase tracking-wide mb-1">Recording Time</h4>
              <p className="text-white">{formatDateTime(footage.startTime)}</p>
              <p className="text-[#8A8A8A] text-xs">Duration: {formatDuration(footage.duration)}</p>
            </div>

            {/* Technical info */}
            <div>
              <h4 className="text-[#8A8A8A] text-xs uppercase tracking-wide mb-1">Technical</h4>
              <p className="text-white">{footage.resolution}</p>
              <p className="text-[#8A8A8A] text-xs">Size: {footage.fileSize.toFixed(1)} MB</p>
            </div>

            {/* Features */}
            <div>
              <h4 className="text-[#8A8A8A] text-xs uppercase tracking-wide mb-1">Features</h4>
              <div className="flex flex-wrap gap-2">
                {footage.faceDetected && (
                  <div className="flex items-center gap-1 text-blue-400 text-xs">
                    <BsPersonCheck size={12} />
                    <span>Face</span>
                  </div>
                )}
                {footage.motionDetected && (
                  <div className="flex items-center gap-1 text-green-400 text-xs">
                    <MdMotionPhotosOn size={12} />
                    <span>Motion</span>
                  </div>
                )}
                {footage.hasAudio && (
                  <div className="flex items-center gap-1 text-purple-400 text-xs">
                    <HiVolumeUp size={12} />
                    <span>Audio</span>
                  </div>
                )}
              </div>
            </div>

            {/* Tags */}
            <div>
              <h4 className="text-[#8A8A8A] text-xs uppercase tracking-wide mb-1">Tags</h4>
              <div className="flex flex-wrap gap-1">
                {footage.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="bg-[#3D3D3D] text-[#BFBFBF] px-2 py-1 rounded text-xs"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FootageVideoPlayer;
