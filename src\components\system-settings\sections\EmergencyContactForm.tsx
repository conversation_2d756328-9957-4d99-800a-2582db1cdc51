"use client";
import React, { useState } from "react";
import { TextField } from "@/components/InputField";
import { SubmitButton } from "@/components/button";
import { useEmergencyContactForm, EmergencyContactFormData, EmergencyContact } from "./hooks";
import { FiChevronDown } from "react-icons/fi";

interface EmergencyContactFormProps {
  onSubmit: (formData: EmergencyContactFormData) => Promise<void>;
  contactType: "primary" | "backup";
  editingContact?: EmergencyContact | null;
}

const EmergencyContactForm: React.FC<EmergencyContactFormProps> = ({
  onSubmit,
  contactType,
  editingContact,
}) => {
  const [isTypeDropdownOpen, setIsTypeDropdownOpen] = useState(false);

  const {
    formData,
    errors,
    focusedField,
    submitAttempted,
    handleChange,
    handleTypeChange,
    handleFocus,
    handleBlur,
    handleSubmit,
    isFormValid,
  } = useEmergencyContactForm(onSubmit, editingContact);

  const contactTypes = [
    "Police",
    "Private Security",
    "Internal Admin",
    "Fire Service",
    "Medical",
  ];

  const handleTypeSelect = (type: string) => {
    handleTypeChange(type);
    setIsTypeDropdownOpen(false);
  };

  const getTitle = () => {
    return contactType === "primary" 
      ? "Add Primary Emergency Contact" 
      : "Add Backup Emergency Contact";
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Phone Number Field */}
      <TextField
        id="phoneNumber"
        label="Phone Number"
        type="tel"
        placeholder="Enter Number"
        value={formData.phoneNumber}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        error={errors.phoneNumber}
        isFocused={focusedField === "phoneNumber"}
        submitAttempted={submitAttempted}
        showErrorAlways={true}
      />

      {/* Type Dropdown */}
      <div>
        <label htmlFor="type" className="block text-sm text-[#E4E7EC] mb-1">
          Type
        </label>
        <div className="relative">
          <button
            type="button"
            onClick={() => setIsTypeDropdownOpen(!isTypeDropdownOpen)}
            className={`w-full bg-[#1F1F1F] text-left py-3 px-4 focus:outline-none focus:ring-1 flex items-center justify-between ${
              errors.type && (focusedField === "type" || submitAttempted)
                ? "border border-red-500 focus:ring-red-500"
                : "focus:ring-[#E4E7EC]"
            } ${formData.type ? "text-white" : "text-[#A3A3A3]"}`}
          >
            <span>{formData.type || "Choose Type"}</span>
            <FiChevronDown 
              className={`transition-transform duration-200 ${
                isTypeDropdownOpen ? "rotate-180" : ""
              }`} 
            />
          </button>

          {/* Dropdown Menu */}
          {isTypeDropdownOpen && (
            <div className="absolute top-full left-0 right-0 z-10 bg-[#1F1F1F] border border-[#3D3D3D] mt-1 max-h-48 overflow-y-auto">
              {contactTypes.map((type) => (
                <button
                  key={type}
                  type="button"
                  onClick={() => handleTypeSelect(type)}
                  className="w-full text-left px-4 py-3 text-white hover:bg-[#2E2E2E] transition-colors"
                >
                  {type}
                </button>
              ))}
            </div>
          )}
        </div>
        {errors.type && (focusedField === "type" || submitAttempted) && (
          <p className="text-red-500 text-xs mt-1 error-message">{errors.type}</p>
        )}
      </div>

      {/* Submit Button */}
      <div className="flex justify-center w-full pt-4">
        <SubmitButton
          label={editingContact ? "Save Changes" : "Save Emergency Contact"}
          type="submit"
          disabled={!isFormValid}
          className={!isFormValid ? "opacity-50 cursor-not-allowed" : ""}
        />
      </div>
    </form>
  );
};

export default EmergencyContactForm;
