"use client";
import React, { useState } from "react";
import { DetailedModal } from "@/components/Modal";
import EmergencyContactForm from "./EmergencyContactForm";
import EmergencyContactSuccessModal from "./EmergencyContactSuccessModal";
import { EmergencyContactFormData, EmergencyContact } from "./hooks";

interface EmergencyContactModalProps {
  isOpen: boolean;
  onClose: () => void;
  onContactAdded: (contact: EmergencyContact) => void;
  contactType: "primary" | "backup";
  editingContact?: EmergencyContact | null;
}

const EmergencyContactModal: React.FC<EmergencyContactModalProps> = ({
  isOpen,
  onClose,
  onContactAdded,
  contactType,
  editingContact,
}) => {
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [savedContact, setSavedContact] = useState<EmergencyContact | null>(null);

  const handleFormSubmit = async (formData: EmergencyContactFormData) => {
    try {
      // Here you would typically make an API call to save the emergency contact
      console.log("Emergency contact data:", formData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Create the contact object
      const newContact: EmergencyContact = {
        id: editingContact?.id || Date.now().toString(),
        phoneNumber: formData.phoneNumber,
        type: formData.type,
        contactType: contactType,
      };

      // Store the contact for later use
      setSavedContact(newContact);

      // Show success modal
      setShowSuccessModal(true);
    } catch (error) {
      console.error("Error saving emergency contact:", error);
      // Handle error - could show error toast here
    }
  };

  const handleModalClose = () => {
    setShowSuccessModal(false);
    onClose();
  };

  const handleSuccessClose = () => {
    setShowSuccessModal(false);
    // Pass the saved contact data to parent component
    if (savedContact) {
      onContactAdded(savedContact);
    }
    setSavedContact(null);
  };

  const getTitle = () => {
    if (editingContact) {
      return contactType === "primary"
        ? "Edit Emergency Number"
        : "Edit Backup Line";
    }
    return contactType === "primary"
      ? "Add Emergency Number"
      : "Add Backup Line";
  };

  const getDescription = () => {
    if (editingContact) {
      return contactType === "primary"
        ? "Update your primary emergency contact details"
        : "Update your backup emergency line details";
    }
    return contactType === "primary"
      ? "Add your primary emergency contact for critical alerts"
      : "Add a backup emergency line for redundancy";
  };

  return (
    <>
      <DetailedModal isOpen={isOpen && !showSuccessModal} onClose={handleModalClose}>
        <div className="w-full">
          <div className="mb-6 border-b border-[#3D3D3D] pb-4">
            <h2 className="text-lg lg:text-xl text-white font-medium mb-2">
              {getTitle()}
            </h2>
            <p className="text-sm text-[#A3A3A3]">
              {getDescription()}
            </p>
          </div>

          <EmergencyContactForm
            onSubmit={handleFormSubmit}
            contactType={contactType}
            editingContact={editingContact}
          />
        </div>
      </DetailedModal>

      <EmergencyContactSuccessModal
        isOpen={showSuccessModal}
        onClose={handleSuccessClose}
      />
    </>
  );
};

export default EmergencyContactModal;
